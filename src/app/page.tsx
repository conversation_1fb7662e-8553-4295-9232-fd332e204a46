'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import {
  Scale,
  Users,
  FileText,
  Award,
  Shield,
  Building,
  Gavel,
  BookOpen,
  Download,
  MessageCircle,
  LogIn,
  Phone,
  Mail,
  MapPin,
  Star,
  TrendingUp,
  CheckCircle,
  ArrowRight
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'

interface CompanyData {
  id: number
  name: string
  logo_url?: string
  phone?: string
  email?: string
  address?: string
  city?: string
  country?: string
  description?: string
  established_date?: string
}

interface Stats {
  lawyers: number
  cases: number
  clients: number
  completed_cases: number
  new_cases: number
  success_rate: number
}

export default function HomePage() {
  const [companyData, setCompanyData] = useState<CompanyData | null>(null)
  const [stats] = useState<Stats>({
    lawyers: 25,
    cases: 1250,
    clients: 850,
    completed_cases: 980,
    new_cases: 45,
    success_rate: 95
  })

  useEffect(() => {
    fetchCompanyData()
  }, [])

  const fetchCompanyData = async () => {
    try {
      const response = await fetch('/api/company')
      const result = await response.json()
      if (result.success && result.data.length > 0) {
        setCompanyData(result.data[0])
      }
    } catch (error) {
      console.error('Error fetching company data:', error)
    }
  }

  const services = [
    { id: 1, title: 'الاستشارات القانونية', icon: Scale, description: 'استشارات قانونية شاملة في جميع فروع القانون' },
    { id: 2, title: 'التمثيل القضائي', icon: Gavel, description: 'تمثيل قانوني محترف أمام جميع المحاكم والجهات القضائية' },
    { id: 3, title: 'صياغة العقود', icon: FileText, description: 'صياغة ومراجعة العقود والاتفاقيات القانونية' },
    { id: 4, title: 'قضايا الأحوال الشخصية', icon: Users, description: 'خدمات قانونية متخصصة في قضايا الأسرة والأحوال الشخصية' },
    { id: 5, title: 'القضايا التجارية', icon: Building, description: 'حلول قانونية للشركات والمؤسسات التجارية' },
    { id: 6, title: 'قضايا العمل', icon: Shield, description: 'حماية حقوق العمال وأصحاب العمل' }
  ]

  return (
    <div className="min-h-screen bg-white">
      <header className="bg-white shadow-sm border-b sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              {companyData?.logo_url && companyData.logo_url.trim() !== '' && companyData.logo_url.startsWith('http') ? (
                <Image
                  src={companyData.logo_url}
                  alt="شعار الشركة"
                  width={60}
                  height={60}
                  className="rounded-lg mr-3"
                />
              ) : (
                <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                  <Scale className="h-6 w-6 text-white" />
                </div>
              )}
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  {companyData?.name || 'مؤسسة المحاماة والاستشارات القانونية'}
                </h1>
                <p className="text-sm text-gray-600">Excellence in Legal Services</p>
              </div>
            </div>

            <nav className="hidden md:flex items-center space-x-8 space-x-reverse">
              <Link href="#home" className="text-gray-700 hover:text-blue-600 font-medium">الرئيسية</Link>
              <Link href="#services" className="text-gray-700 hover:text-blue-600 font-medium">خدماتنا</Link>
              <Link href="#about" className="text-gray-700 hover:text-blue-600 font-medium">من نحن</Link>
              <Link href="#library" className="text-gray-700 hover:text-blue-600 font-medium">المكتبة القانونية</Link>
            </nav>

            <Link href="/dashboard">
              <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                <LogIn className="h-4 w-4 mr-2" />
                دخول النظام
              </Button>
            </Link>
          </div>
        </div>
      </header>

      <section id="home" className="relative bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 py-24 overflow-hidden">
        <div className="container mx-auto px-4 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="text-white">
              <div className="inline-flex items-center bg-blue-600/20 backdrop-blur-sm border border-blue-400/30 rounded-full px-4 py-2 mb-6">
                <Scale className="h-4 w-4 mr-2 text-blue-300" />
                <span className="text-sm font-medium text-blue-100">مكتب محاماة معتمد</span>
              </div>

              <h1 className="text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                <span className="text-white">العدالة</span>
                <span className="text-blue-400 block">والاحترافية</span>
                <span className="text-white">في خدمة القانون</span>
              </h1>

              <p className="text-xl text-gray-300 mb-8 leading-relaxed max-w-lg">
                {companyData?.description?.substring(0, 150) ||
                  'نقدم خدمات قانونية شاملة ومتخصصة بأعلى معايير الجودة والاحترافية لضمان حماية حقوقك وتحقيق أهدافك القانونية'
                }...
              </p>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg font-semibold shadow-xl">
                  <MessageCircle className="h-5 w-5 mr-2" />
                  استشارة مجانية
                </Button>
                <Button size="lg" variant="outline" className="border-2 border-white/30 text-white hover:bg-white/10 backdrop-blur-sm px-8 py-4 text-lg font-semibold">
                  <Phone className="h-5 w-5 mr-2" />
                  {companyData?.phone || '+967-1-234567'}
                </Button>
              </div>
            </div>

            <div className="relative">
              <div className="relative bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 shadow-2xl">
                <div className="absolute -top-4 -right-4 w-24 h-24 bg-blue-600 rounded-2xl flex items-center justify-center shadow-xl">
                  <Scale className="h-12 w-12 text-white" />
                </div>

                <div className="mt-8 space-y-6">
                  <div className="flex items-center justify-between p-4 bg-white/10 rounded-xl backdrop-blur-sm">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-green-400 rounded-full mr-3"></div>
                      <span className="text-white font-medium">قضايا منجزة بنجاح</span>
                    </div>
                    <span className="text-2xl font-bold text-white">{stats.completed_cases}+</span>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-white/10 rounded-xl backdrop-blur-sm">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-blue-400 rounded-full mr-3"></div>
                      <span className="text-white font-medium">عملاء راضون</span>
                    </div>
                    <span className="text-2xl font-bold text-white">{stats.clients}+</span>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-white/10 rounded-xl backdrop-blur-sm">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-yellow-400 rounded-full mr-3"></div>
                      <span className="text-white font-medium">نسبة النجاح</span>
                    </div>
                    <span className="text-2xl font-bold text-white">{stats.success_rate}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section id="services" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">خدماتنا القانونية</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              نقدم مجموعة شاملة من الخدمات القانونية المتخصصة لتلبية احتياجاتكم القانونية
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service) => (
              <Card key={service.id} className="hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                      <service.icon className="h-6 w-6 text-blue-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">{service.title}</h3>
                  </div>
                  <p className="text-gray-600 text-sm leading-relaxed">{service.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <section id="about" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-bold text-gray-900 mb-6">من نحن</h2>
              <div className="space-y-4 text-gray-600 leading-relaxed">
                <p>
                  {companyData?.description ||
                    'نحن مؤسسة قانونية متخصصة تقدم خدمات قانونية شاملة ومتميزة للأفراد والشركات. نتميز بخبرتنا الواسعة وفريقنا المتخصص من المحامين والمستشارين القانونيين.'
                  }
                </p>
                <p>
                  تأسست مؤسستنا في عام {companyData?.established_date ? new Date(companyData.established_date).getFullYear() : '2020'}
                  ومنذ ذلك الحين ونحن نعمل بجد لتقديم أفضل الخدمات القانونية وحماية حقوق عملائنا.
                </p>
              </div>
            </div>

            <div className="bg-blue-50 rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">معلومات التواصل</h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <Phone className="h-5 w-5 text-blue-600 mr-3" />
                  <span className="text-gray-700">{companyData?.phone || '+967-1-234567'}</span>
                </div>
                <div className="flex items-center">
                  <Mail className="h-5 w-5 text-blue-600 mr-3" />
                  <span className="text-gray-700">{companyData?.email || '<EMAIL>'}</span>
                </div>
                <div className="flex items-center">
                  <MapPin className="h-5 w-5 text-blue-600 mr-3" />
                  <span className="text-gray-700">
                    {companyData?.address || 'العنوان غير محدد'}, {companyData?.city || 'المدينة'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section id="library" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">المكتبة القانونية</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              مجموعة شاملة من القوانين والتشريعات والوثائق القانونية المهمة
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="hover:shadow-xl transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mr-4">
                    <FileText className="h-6 w-6 text-red-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">القانون المدني</h3>
                    <p className="text-sm text-gray-500">PDF • 2.5 MB</p>
                  </div>
                </div>
                <p className="text-gray-600 text-sm mb-4">
                  النص الكامل للقانون المدني مع التعديلات الأخيرة
                </p>
                <Button size="sm" variant="outline" className="w-full">
                  <Download className="h-4 w-4 mr-2" />
                  تحميل الملف
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-xl transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <FileText className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">قانون التجارة</h3>
                    <p className="text-sm text-gray-500">PDF • 1.8 MB</p>
                  </div>
                </div>
                <p className="text-gray-600 text-sm mb-4">
                  قانون التجارة والشركات التجارية مع اللوائح التنفيذية
                </p>
                <Button size="sm" variant="outline" className="w-full">
                  <Download className="h-4 w-4 mr-2" />
                  تحميل الملف
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-xl transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                    <FileText className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">قانون العمل</h3>
                    <p className="text-sm text-gray-500">PDF • 1.2 MB</p>
                  </div>
                </div>
                <p className="text-gray-600 text-sm mb-4">
                  قانون العمل وحقوق العمال مع التعديلات الأخيرة
                </p>
                <Button size="sm" variant="outline" className="w-full">
                  <Download className="h-4 w-4 mr-2" />
                  تحميل الملف
                </Button>
              </CardContent>
            </Card>
          </div>

          <div className="text-center mt-12">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white">
              <BookOpen className="h-5 w-5 mr-2" />
              تصفح المكتبة الكاملة
            </Button>
          </div>
        </div>
      </section>

      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="md:col-span-2">
              <div className="flex items-center mb-4">
                {companyData?.logo_url ? (
                  <Image
                    src={companyData.logo_url}
                    alt="شعار الشركة"
                    width={40}
                    height={40}
                    className="rounded-lg mr-3"
                  />
                ) : (
                  <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                    <Scale className="h-5 w-5 text-white" />
                  </div>
                )}
                <h3 className="text-xl font-bold">
                  {companyData?.name || 'مؤسسة المحاماة والاستشارات القانونية'}
                </h3>
              </div>
              <p className="text-gray-300 mb-4 leading-relaxed">
                {companyData?.description?.substring(0, 200) || 'مؤسسة قانونية متخصصة تقدم خدمات قانونية شاملة ومتميزة'}...
              </p>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">خدماتنا</h4>
              <ul className="space-y-2 text-gray-300">
                <li>الاستشارات القانونية</li>
                <li>التمثيل القضائي</li>
                <li>صياغة العقود</li>
                <li>قضايا الأحوال الشخصية</li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">معلومات التواصل</h4>
              <div className="space-y-2 text-gray-300">
                <p>{companyData?.phone || '+967-1-234567'}</p>
                <p>{companyData?.email || '<EMAIL>'}</p>
                <p>{companyData?.address || 'العنوان غير محدد'}</p>
                <p>{companyData?.city || 'المدينة'}, {companyData?.country || 'البلد'}</p>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; {new Date().getFullYear()} {companyData?.name || 'مؤسسة المحاماة والاستشارات القانونية'}. جميع الحقوق محفوظة.</p>
          </div>
        </div>
      </footer>

      <div className="fixed bottom-6 left-6 z-50">
        <Button
          size="lg"
          className="bg-green-600 hover:bg-green-700 text-white rounded-full shadow-lg"
          onClick={() => alert('ميزة الدردشة قيد التطوير')}
        >
          <MessageCircle className="h-6 w-6" />
        </Button>
      </div>
    </div>
  )
}