'use client';

import React from 'react';

interface Stat {
  clients: number;
  issues: number;
  employees: number;
  completedIssues: number;
  newIssues: number;
  courts: number;
  successRate: number;
  experienceYears: number;
}

interface StatisticsSectionProps {
  stats: Stat;
}

export function StatisticsSection({ stats }: StatisticsSectionProps) {
  const statisticsData = [
    {
      number: stats.clients.toLocaleString(),
      label: 'عميل راضٍ',
      icon: '👥'
    },
    {
      number: stats.completedIssues.toLocaleString(),
      label: 'قضية منجزة',
      icon: '⚖️'
    },
    {
      number: stats.employees.toString(),
      label: 'محامٍ متخصص',
      icon: '👨‍💼'
    },
    {
      number: stats.experienceYears.toString(),
      label: 'سنة خبرة',
      icon: '🏆'
    },
    {
      number: `${stats.successRate}%`,
      label: 'معدل النجاح',
      icon: '📈'
    },
    {
      number: stats.courts.toString(),
      label: 'محكمة نتعامل معها',
      icon: '🏛️'
    }
  ];

  return (
    <section className="py-16 bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            إنجازاتنا بالأرقام
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            نفخر بما حققناه من إنجازات وثقة عملائنا على مدار سنوات عملنا
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {statisticsData.map((stat, index) => (
            <div
              key={index}
              className="bg-white rounded-lg shadow-lg p-8 text-center hover:shadow-xl transition-shadow duration-300"
            >
              <div className="text-4xl mb-4">{stat.icon}</div>
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {stat.number}
              </div>
              <div className="text-gray-600 font-medium">
                {stat.label}
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <div className="bg-white rounded-lg shadow-lg p-8 max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">
              لماذا نحن الخيار الأفضل؟
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-right">
              <div className="flex items-start space-x-3 space-x-reverse">
                <div className="text-green-500 text-xl">✓</div>
                <div>
                  <h4 className="font-semibold text-gray-900">خبرة واسعة</h4>
                  <p className="text-gray-600 text-sm">
                    أكثر من {stats.experienceYears} سنة في المجال القانوني
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3 space-x-reverse">
                <div className="text-green-500 text-xl">✓</div>
                <div>
                  <h4 className="font-semibold text-gray-900">معدل نجاح عالي</h4>
                  <p className="text-gray-600 text-sm">
                    {stats.successRate}% معدل نجاح في القضايا المختلفة
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3 space-x-reverse">
                <div className="text-green-500 text-xl">✓</div>
                <div>
                  <h4 className="font-semibold text-gray-900">فريق متخصص</h4>
                  <p className="text-gray-600 text-sm">
                    {stats.employees} محامٍ متخصص في مختلف المجالات
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3 space-x-reverse">
                <div className="text-green-500 text-xl">✓</div>
                <div>
                  <h4 className="font-semibold text-gray-900">ثقة العملاء</h4>
                  <p className="text-gray-600 text-sm">
                    أكثر من {stats.clients.toLocaleString()} عميل راضٍ عن خدماتنا
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
